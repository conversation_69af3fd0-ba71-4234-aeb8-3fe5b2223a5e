{% extends 'base.html' %}

{% block title %}Loan Management - LibraryPro{% endblock %}

{% block content %}
<div class="space-y-8 animate-fade-in">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
            <h1 class="text-4xl font-bold text-gray-800 mb-2">Loan Management</h1>
            <p class="text-gray-600">Track and manage all book loans efficiently</p>
        </div>
        <div class="mt-4 lg:mt-0 flex space-x-3">
            <a href="{% url 'loan_create' %}" class="btn-primary px-6 py-3 rounded-xl text-white font-semibold flex items-center space-x-2 shadow-lg">
                <i class="fas fa-plus"></i>
                <span>Issue New Loan</span>
            </a>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-2xl shadow-xl p-6">
        <form method="get" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <div class="relative">
                        <input type="text" name="search" value="{{ search_query }}" 
                               placeholder="Search by book title, borrower name..."
                               class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300">
                        <option value="">All Status</option>
                        {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="flex items-end space-x-2">
                    <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300">
                        <i class="fas fa-search mr-2"></i>Filter
                    </button>
                    <a href="{% url 'loan_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Loans List -->
    {% if loans %}
    <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800">Active Loans</h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Book</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Borrower</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for loan in loans %}
                    <tr class="hover:bg-gray-50 transition-colors duration-200" data-loan-id="{{ loan.id }}">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-12 h-16 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                                    {% if loan.book.cover_image %}
                                    <img src="{{ loan.book.cover_image.url }}" alt="{{ loan.book.title }}" class="w-12 h-16 object-cover rounded-lg">
                                    {% else %}
                                    <i class="fas fa-book text-gray-400"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">{{ loan.book.title }}</div>
                                    <div class="text-sm text-gray-500">{{ loan.book.isbn }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="font-medium text-gray-900">{{ loan.borrower.get_full_name }}</div>
                            <div class="text-sm text-gray-500">{{ loan.borrower.get_role_display }}</div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            {{ loan.issue_date|date:"M d, Y" }}
                        </td>
                        <td class="px-6 py-4 text-sm">
                            <div class="due-date {% if loan.is_overdue %}text-red-600 font-medium{% else %}text-gray-900{% endif %}">
                                {{ loan.due_date|date:"M d, Y" }}
                            </div>
                            {% if loan.is_overdue %}
                            <div class="text-xs text-red-500">{{ loan.days_overdue }} days overdue</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                {% if loan.status == 'active' %}bg-blue-100 text-blue-800
                                {% elif loan.status == 'returned' %}bg-green-100 text-green-800
                                {% elif loan.status == 'overdue' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                <i class="fas fa-circle text-xs mr-1"></i>
                                {{ loan.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm font-medium space-x-2">
                            {% if loan.status == 'active' %}
                            <a href="{% url 'loan_return' loan.id %}" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-lg text-xs transition-colors duration-200">
                                <i class="fas fa-undo mr-1"></i>Return
                            </a>
                            {% if loan.can_renew %}
                            <button onclick="renewLoan('{{ loan.id }}')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg text-xs transition-colors duration-200">
                                <i class="fas fa-refresh mr-1"></i>Renew
                            </button>
                            {% endif %}
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex justify-center">
        <nav class="flex space-x-2">
            {% if page_obj.has_previous %}
            <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                First
            </a>
            <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                Previous
            </a>
            {% endif %}

            <span class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                Next
            </a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                Last
            </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- No Loans Found -->
    <div class="text-center py-16">
        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-hand-holding text-4xl text-gray-400"></i>
        </div>
        <h3 class="text-xl font-medium text-gray-500 mb-2">No loans found</h3>
        <p class="text-gray-400 mb-6">No loans match your current filters.</p>
        <a href="{% url 'loan_create' %}" class="btn-primary px-6 py-3 rounded-xl text-white font-semibold">
            <i class="fas fa-plus mr-2"></i>Issue First Loan
        </a>
    </div>
    {% endif %}
</div>

<script>
function renewLoan(loanId) {
    console.log('🔄 Renew loan clicked for ID:', loanId);

    if (confirm('Are you sure you want to renew this loan? This will extend the due date by 14 days.')) {
        console.log('✅ User confirmed renewal');

        // Show loading state
        const renewBtn = document.querySelector(`button[onclick="renewLoan('${loanId}')"]`);
        if (renewBtn) {
            const originalText = renewBtn.innerHTML;
            renewBtn.disabled = true;
            renewBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Renewing...';

            // Make AJAX call to renew the loan
            fetch(`/loans/${loanId}/renew/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                console.log('📡 Renewal response:', data);

                if (data.success) {
                    // Update the due date in the table
                    const dueDateCell = document.querySelector(`tr[data-loan-id="${loanId}"] .due-date`);
                    if (dueDateCell) {
                        dueDateCell.textContent = data.new_due_date;
                    }

                    // Show success message
                    showNotification('Loan renewed successfully!', 'success');

                    // Update renewal count if displayed
                    const renewalCell = document.querySelector(`tr[data-loan-id="${loanId}"] .renewal-count`);
                    if (renewalCell) {
                        renewalCell.textContent = data.renewal_count;
                    }

                    // Check if loan can still be renewed
                    if (!data.can_renew) {
                        renewBtn.style.display = 'none';
                    }
                } else {
                    showNotification(data.error || 'Failed to renew loan', 'error');
                }
            })
            .catch(error => {
                console.error('❌ Renewal error:', error);
                showNotification('Error renewing loan. Please try again.', 'error');
            })
            .finally(() => {
                // Restore button state
                renewBtn.disabled = false;
                renewBtn.innerHTML = originalText;
            });
        }
    } else {
        console.log('❌ User cancelled renewal');
    }
}

// Simple notification function
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

console.log('🔍 Loan list script loaded');
</script>
{% endblock %}
