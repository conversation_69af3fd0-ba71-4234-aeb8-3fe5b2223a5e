{% extends 'base.html' %}

{% block title %}My Profile - LibraryPro{% endblock %}

{% block content %}
<div class="space-y-8 animate-fade-in">
    <!-- Profile Header -->
    <div class="relative overflow-hidden bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-3xl shadow-2xl">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
        <div class="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
        
        <div class="relative px-8 py-12">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="flex items-center space-x-6">
                    <div class="relative">
                        <div class="w-24 h-24 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                            {% if user.profile_picture %}
                            <img src="{{ user.profile_picture.url }}" alt="Profile" class="w-24 h-24 rounded-2xl object-cover">
                            {% else %}
                            <i class="fas fa-user text-4xl text-white"></i>
                            {% endif %}
                        </div>
                        <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
                            <i class="fas fa-check text-white text-xs"></i>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-4xl font-bold text-white mb-2">
                            {{ user.get_full_name|default:user.username }}
                        </h1>
                        <div class="flex flex-wrap items-center gap-3 mb-3">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm">
                                <i class="fas fa-user-tag mr-2"></i>{{ user.get_role_display }}
                            </span>
                            {% if user.enrollment_number %}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm">
                                <i class="fas fa-id-card mr-2"></i>{{ user.enrollment_number }}
                            </span>
                            {% endif %}
                            {% if user.class_grade %}
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm">
                                <i class="fas fa-graduation-cap mr-2"></i>{{ user.class_grade }}
                            </span>
                            {% endif %}
                        </div>
                        <p class="text-white/90 text-lg">
                            Member since {{ user.date_joined|date:"F Y" }}
                        </p>
                    </div>
                </div>
                
                <div class="mt-6 lg:mt-0">
                    <button onclick="toggleEditMode()" class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 backdrop-blur-sm">
                        <i class="fas fa-edit mr-2"></i>Edit Profile
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Information -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Personal Information -->
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                    <i class="fas fa-user-circle text-blue-600 mr-3"></i>
                    Personal Information
                </h2>
                
                <div id="profileView">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">First Name</label>
                            <p class="text-lg text-gray-900">{{ user.first_name|default:"Not provided" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Last Name</label>
                            <p class="text-lg text-gray-900">{{ user.last_name|default:"Not provided" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Email</label>
                            <p class="text-lg text-gray-900">{{ user.email|default:"Not provided" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Additional Notification Email</label>
                            <p class="text-lg text-gray-900">{{ user.notification_email|default:"Not provided" }}</p>
                            {% if user.notification_email %}
                            <p class="text-xs text-gray-500 mt-1">This email also receives library notifications</p>
                            {% endif %}
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Phone Number</label>
                            <p class="text-lg text-gray-900">{{ user.phone_number|default:"Not provided" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Date of Birth</label>
                            <p class="text-lg text-gray-900">{{ user.date_of_birth|date:"F d, Y"|default:"Not provided" }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Member Status</label>
                            <p class="text-lg">
                                {% if user.is_active_member %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Active
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle mr-1"></i>Inactive
                                </span>
                                {% endif %}
                            </p>
                        </div>
                        {% if user.address %}
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-500 mb-1">Address</label>
                            <p class="text-lg text-gray-900">{{ user.address }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Edit Form (Hidden by default) -->
                <div id="profileEdit" class="hidden">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                {{ form.first_name }}
                            </div>
                            <div>
                                <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                {{ form.last_name }}
                            </div>
                            <div>
                                <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                {{ form.email }}
                            </div>
                            <div>
                                <label for="{{ form.notification_email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Additional Notification Email</label>
                                {{ form.notification_email }}
                                <p class="mt-1 text-xs text-gray-500">Optional additional email for library notifications (parent, guardian, or secondary contact)</p>
                            </div>
                            <div>
                                <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                {{ form.phone_number }}
                            </div>
                            <div>
                                <label for="{{ form.date_of_birth.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                                {{ form.date_of_birth }}
                            </div>
                            {% if user.role == 'student' %}
                            <div>
                                <label for="{{ form.class_grade.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Class/Grade</label>
                                {{ form.class_grade }}
                            </div>
                            {% endif %}
                            <div class="md:col-span-2">
                                <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                                {{ form.address }}
                            </div>
                            <div class="md:col-span-2">
                                <label for="{{ form.profile_picture.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Profile Picture</label>
                                {{ form.profile_picture }}
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-4 mt-8">
                            <button type="button" onclick="toggleEditMode()" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300">
                                Cancel
                            </button>
                            <button type="submit" id="profileSubmitBtn" class="btn-primary px-6 py-3 rounded-xl text-white font-medium shadow-lg">
                                <i class="fas fa-save mr-2"></i><span id="profileSubmitText">Save Changes</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Activity Summary -->
        <div class="space-y-8">
            <!-- Quick Stats -->
            <div class="bg-white rounded-2xl shadow-xl p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-bar text-green-600 mr-2"></i>
                    My Activity
                </h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-xl">
                        <span class="text-blue-800 font-medium">Current Loans</span>
                        <span class="text-2xl font-bold text-blue-600">{{ loans.count }}</span>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-xl">
                        <span class="text-yellow-800 font-medium">Reservations</span>
                        <span class="text-2xl font-bold text-yellow-600">{{ reservations.count }}</span>
                    </div>
                </div>
            </div>

            <!-- Recent Loans -->
            {% if loans %}
            <div class="bg-white rounded-2xl shadow-xl p-6">
                <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-history text-purple-600 mr-2"></i>
                    Recent Loans
                </h3>
                <div class="space-y-3">
                    {% for loan in loans %}
                    <div class="flex items-center p-3 bg-gray-50 rounded-xl">
                        <div class="w-10 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-book text-gray-400 text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium text-gray-900 text-sm">{{ loan.book.title|truncatechars:30 }}</div>
                            <div class="text-xs text-gray-500">Due: {{ loan.due_date|date:"M d" }}</div>
                        </div>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                            {% if loan.status == 'active' %}bg-blue-100 text-blue-800
                            {% elif loan.status == 'overdue' %}bg-red-100 text-red-800
                            {% else %}bg-green-100 text-green-800{% endif %}">
                            {{ loan.get_status_display }}
                        </span>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function toggleEditMode() {
    const profileView = document.getElementById('profileView');
    const profileEdit = document.getElementById('profileEdit');

    if (profileView.classList.contains('hidden')) {
        profileView.classList.remove('hidden');
        profileEdit.classList.add('hidden');
    } else {
        profileView.classList.add('hidden');
        profileEdit.classList.remove('hidden');
    }
}

// Add form control classes to Django form fields
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('#profileEdit input, #profileEdit textarea, #profileEdit select');
    inputs.forEach(input => {
        input.classList.add('w-full', 'px-4', 'py-3', 'border', 'border-gray-300', 'rounded-xl', 'focus:ring-2', 'focus:ring-blue-500', 'focus:border-transparent', 'transition-all', 'duration-300');
    });

    // Add form submission handling
    const profileForm = document.querySelector('#profileEdit form');
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('profileSubmitBtn');
            const submitText = document.getElementById('profileSubmitText');

            if (submitBtn && submitText) {
                submitBtn.disabled = true;
                submitText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';

                // Re-enable button after 15 seconds as fallback
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitText.innerHTML = 'Save Changes';
                }, 15000);
            }

            // Allow form submission to proceed
            return true;
        });
    }
});
</script>
{% endblock %}
