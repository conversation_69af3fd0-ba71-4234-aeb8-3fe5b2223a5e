{% extends 'base.html' %}
{% load static %}

{% block title %}Reservations - LibraryPro{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Book Reservations</h1>
                <p class="text-gray-600 mt-2">Manage student book reservations and waiting lists</p>
            </div>
            <div class="flex space-x-4">
                <a href="{% url 'pending_requests' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-sm">
                    <i class="fas fa-clock mr-2"></i>Pending Requests
                </a>
                <a href="{% url 'loan_list' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-sm">
                    <i class="fas fa-list mr-2"></i>All Loans
                </a>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
        <form method="get" class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <input type="text" name="search" value="{{ search_query }}" 
                       placeholder="Search by book title, student name..." 
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div class="md:w-48">
                <select name="status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Status</option>
                    {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="flex gap-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200">
                    <i class="fas fa-search mr-2"></i>Search
                </button>
                {% if search_query or status_filter %}
                <a href="{% url 'reservation_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200">
                    <i class="fas fa-times mr-2"></i>Clear
                </a>
                {% endif %}
            </div>
        </form>
    </div>

    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bookmark text-yellow-600 text-xl"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Reservations</p>
                    <p class="text-2xl font-bold text-gray-900">{{ reservations|length }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservations List -->
    {% if reservations %}
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Reservations</h2>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Book</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reserved Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for reservation in reservations %}
                    <tr class="hover:bg-gray-50 transition-colors duration-200">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="w-12 h-16 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                                    {% if reservation.book.cover_image %}
                                    <img src="{{ reservation.book.cover_image.url }}" alt="{{ reservation.book.title }}" class="w-12 h-16 object-cover rounded-lg">
                                    {% else %}
                                    <i class="fas fa-book text-gray-400"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">{{ reservation.book.title }}</div>
                                    <div class="text-sm text-gray-500">{{ reservation.book.isbn|default:"No ISBN" }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="font-medium text-gray-900">{{ reservation.user.get_full_name }}</div>
                            <div class="text-sm text-gray-500">{{ reservation.user.get_role_display }}</div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            {{ reservation.reservation_date|date:"M d, Y" }}
                        </td>
                        <td class="px-6 py-4 text-sm">
                            <div class="{% if reservation.expiry_date < today %}text-red-600 font-medium{% else %}text-gray-900{% endif %}">
                                {{ reservation.expiry_date|date:"M d, Y" }}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium
                                {% if reservation.status == 'active' %}bg-yellow-100 text-yellow-800
                                {% elif reservation.status == 'fulfilled' %}bg-green-100 text-green-800
                                {% elif reservation.status == 'cancelled' %}bg-gray-100 text-gray-800
                                {% elif reservation.status == 'expired' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                <i class="fas fa-circle text-xs mr-1"></i>
                                {{ reservation.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            #{{ reservation.priority }}
                        </td>
                        <td class="px-6 py-4 text-sm font-medium space-x-2">
                            {% if reservation.status == 'active' %}
                            <a href="{% url 'reservation_cancel' reservation.id %}" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-xs transition-colors duration-200">
                                <i class="fas fa-times mr-1"></i>Cancel
                            </a>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex justify-center mt-8">
        <nav class="flex space-x-2">
            {% if page_obj.has_previous %}
            <a href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                First
            </a>
            <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                Previous
            </a>
            {% endif %}

            <span class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                Next
            </a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
               class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm text-gray-500 hover:bg-gray-50 transition-colors duration-200">
                Last
            </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- No Reservations Found -->
    <div class="text-center py-16">
        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-bookmark text-4xl text-gray-400"></i>
        </div>
        <h3 class="text-xl font-medium text-gray-500 mb-2">No reservations found</h3>
        <p class="text-gray-400 mb-6">No reservations match your current filters.</p>
    </div>
    {% endif %}
</div>
{% endblock %}
