{% extends 'base.html' %}

{% block title %}{% if form.instance.pk %}Edit User{% else %}Add User{% endif %}{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-800">
                {% if form.instance.pk %}Edit User{% else %}Add New User{% endif %}
            </h1>
            <p class="text-gray-600 mt-1">
                {% if form.instance.pk %}Update user information and settings{% else %}Create a new user account{% endif %}
            </p>
        </div>
        <a href="{% url 'user_list' %}" 
           class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center">
            <i class="fas fa-arrow-left mr-2"></i>Back to Users
        </a>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
        <form method="post" id="user-form" enctype="multipart/form-data" class="space-y-8">
            {% csrf_token %}
            
            <!-- Account Information -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Account Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Username <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               name="username" 
                               value="{{ form.username.value|default:'' }}"
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                               placeholder="Enter username"
                               required>
                        {% if form.username.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.username.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address <span class="text-red-500">*</span>
                        </label>
                        <input type="email" 
                               name="email" 
                               value="{{ form.email.value|default:'' }}"
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                               placeholder="Enter email address"
                               required>
                        {% if form.email.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.email.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    {% if not form.instance.pk %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Password <span class="text-red-500">*</span>
                        </label>
                        <input type="password" 
                               name="password1" 
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                               placeholder="Enter password"
                               required>
                        {% if form.password1.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.password1.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Confirm Password <span class="text-red-500">*</span>
                        </label>
                        <input type="password" 
                               name="password2" 
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                               placeholder="Confirm password"
                               required>
                        {% if form.password2.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.password2.errors.0 }}</p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Personal Information -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Personal Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input type="text" 
                               name="first_name" 
                               value="{{ form.first_name.value|default:'' }}"
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                               placeholder="Enter first name">
                        {% if form.first_name.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.first_name.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input type="text" 
                               name="last_name" 
                               value="{{ form.last_name.value|default:'' }}"
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                               placeholder="Enter last name">
                        {% if form.last_name.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.last_name.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" 
                               name="phone_number" 
                               value="{{ form.phone_number.value|default:'' }}"
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                               placeholder="Enter phone number">
                        {% if form.phone_number.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.phone_number.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                        <select name="role" 
                                class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm">
                            <option value="student" {% if form.role.value == 'student' %}selected{% endif %}>Student</option>
                            <option value="faculty" {% if form.role.value == 'faculty' %}selected{% endif %}>Faculty</option>
                            <option value="staff" {% if form.role.value == 'staff' %}selected{% endif %}>Staff</option>
                            <option value="librarian" {% if form.role.value == 'librarian' %}selected{% endif %}>Librarian</option>
                            <option value="admin" {% if form.role.value == 'admin' %}selected{% endif %}>Admin</option>
                        </select>
                        {% if form.role.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.role.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                        <textarea name="address" 
                                  rows="3"
                                  class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                                  placeholder="Enter address">{{ form.address.value|default:'' }}</textarea>
                        {% if form.address.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.address.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Academic Information -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Academic Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Student ID</label>
                        <input type="text" 
                               name="student_id" 
                               value="{{ form.student_id.value|default:'' }}"
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                               placeholder="Enter student ID">
                        {% if form.student_id.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.student_id.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Department</label>
                        <input type="text" 
                               name="department" 
                               value="{{ form.department.value|default:'' }}"
                               class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                               placeholder="Enter department">
                        {% if form.department.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.department.errors.0 }}</p>
                        {% endif %}
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Year of Study</label>
                        <select name="year_of_study" 
                                class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm">
                            <option value="">Select year</option>
                            <option value="1" {% if form.year_of_study.value == '1' %}selected{% endif %}>1st Year</option>
                            <option value="2" {% if form.year_of_study.value == '2' %}selected{% endif %}>2nd Year</option>
                            <option value="3" {% if form.year_of_study.value == '3' %}selected{% endif %}>3rd Year</option>
                            <option value="4" {% if form.year_of_study.value == '4' %}selected{% endif %}>4th Year</option>
                            <option value="graduate" {% if form.year_of_study.value == 'graduate' %}selected{% endif %}>Graduate</option>
                        </select>
                        {% if form.year_of_study.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.year_of_study.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Account Status -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Account Status</h3>
                <div class="flex items-center">
                    <input type="checkbox" 
                           name="is_active" 
                           id="is_active"
                           {% if form.is_active.value %}checked{% endif %}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                        Active Account
                    </label>
                </div>
                <p class="mt-1 text-sm text-gray-500">Inactive accounts cannot log in to the system</p>
                {% if form.is_active.errors %}
                <p class="mt-2 text-sm text-red-600">{{ form.is_active.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'user_list' %}" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200">
                    Cancel
                </a>
                <button type="button" id="user-submit-btn"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200"
                        onclick="handleUserSubmit()">
                    <i class="fas fa-save mr-2"></i><span id="user-submit-text">{% if form.instance.pk %}Update User{% else %}Create User{% endif %}</span>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// User form submission handler
function handleUserSubmit() {
    console.log('🖱️ User form submit button clicked!');

    console.log('⏳ Showing loading state...');

    // Show loading state
    const submitBtn = document.getElementById('user-submit-btn');
    const submitText = document.getElementById('user-submit-text');

    if (submitBtn && submitText) {
        submitBtn.disabled = true;
        submitText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
        console.log('✅ Loading state applied');
    }

    console.log('🚀 Submitting user form...');

    // Find the specific user form by ID
    const userForm = document.getElementById('user-form');
    if (userForm) {
        console.log('✅ User form found, submitting...');
        userForm.submit();
    } else {
        console.log('❌ User form not found');
        alert('Error: Could not find the user form to submit.');
        // Restore button state
        if (submitBtn && submitText) {
            submitBtn.disabled = false;
            submitText.innerHTML = '{% if form.instance.pk %}Update User{% else %}Create User{% endif %}';
        }
    }

    return false;
}

console.log('🔧 User form script loaded');
</script>
{% endblock %}
